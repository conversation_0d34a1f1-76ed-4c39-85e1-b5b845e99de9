{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "JSON Library", "Description": "Manage objects, arrays, and primitive data types using JSON.", "Category": "Messaging", "CreatedBy": "Tracer Interactive", "CreatedByURL": "https://tracerinteractive.com", "DocsURL": "https://cdn.tracerinteractive.com/jsonlibrary/documentation.pdf", "MarketplaceURL": "", "SupportURL": "", "EngineVersion": "5.2.0", "CanContainContent": false, "Installed": true, "Modules": [{"Name": "JsonLibrary", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault", "PlatformAllowList": ["Win64"]}, {"Name": "JsonLibraryBlueprintSupport", "Type": "UncookedOnly", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}]}