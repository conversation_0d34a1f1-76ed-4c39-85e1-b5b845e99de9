// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "MiniMapWidgetBase.generated.h"

class UImage;
class USizeBox;

/**
 * 
 */
UCLASS()
class BPFUNCTIONLIB_API UMiniMapWidgetBase : public UUserWidget
{
	GENERATED_BODY()

public:

	virtual void NativePreConstruct() override;

	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:
	UFUNCTION(BlueprintCallable, Category = "Skyline_UI|小地图", meta = (ToolTip = "初始化小地图的参数，内部会调用一次，蓝图调用可刷新内部参数"))
		void InitMinimap();

	UPROPERTY(BlueprintReadOnly, Category = "小地图", meta = (BindWidget))
		UImage* IMG_Map;

	UPROPERTY(BlueprintReadOnly, Category = "小地图", meta = (BindWidget))
		USizeBox* SizeBox_forMap;

	UPROPERTY(BlueprintReadOnly, Category = "小地图", meta = (BindWidget))
		UImage* IMG_Pawn;

	UPROPERTY(BlueprintReadOnly, Category = "小地图", meta = (BindWidget))
		USizeBox* SizeBox_forPawn;

	UPROPERTY(EditAnywhere, Category = "小地图")
		FString ActorNameA = "TopLeft";

	UPROPERTY(EditAnywhere, Category = "小地图")
		FString ActorNameB = "BottomRight";

	UPROPERTY(BlueprintReadWrite, Category = "小地图")
		float AngleOffset = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "小地图", meta = (ToolTip = "使用代理Actor，覆盖当前Pawn的位置与旋转值"))
		bool UseProxyActor = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "小地图")
		FString ProxyActorTag;

protected:
		AActor* TopLeft = nullptr;

		AActor* BottomRight = nullptr;

	float ScaleFactor;
private:
	_forceinline float GetCurrentYaw();

	
	bool bReady = false;
	
	AActor* ActorProxy = nullptr;
};
