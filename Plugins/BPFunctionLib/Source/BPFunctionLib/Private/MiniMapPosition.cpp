// Fill out your copyright notice in the Description page of Project Settings.
// Fill out your copyright notice in the Description page of Project Settings.


#include "MiniMapPosition.h"

// Sets default values
AMiniMapPosition::AMiniMapPosition()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

// Called when the game starts or when spawned
void AMiniMapPosition::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void AMiniMapPosition::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

