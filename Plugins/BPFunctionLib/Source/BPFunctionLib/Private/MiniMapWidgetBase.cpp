// Fill out your copyright notice in the Description page of Project Settings.


#include "MiniMapWidgetBase.h"
#include "EngineUtils.h"
#include "Styling/SlateBrush.h"
#include "Components/Image.h"
#include "Components/SizeBox.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework\Pawn.h"
#include "MiniMapPosition.h"

void UMiniMapWidgetBase::NativePreConstruct()
{
	bReady = false;

	Super::NativePreConstruct();
}

void UMiniMapWidgetBase::NativeTick(const FGeometry & MyGeometry, float InDeltaTime)
{
	if (bReady == false)
	{
		InitMinimap();
	}

	if (bReady == false)
		return;

	AActor* ArrowProxy = nullptr;

	if (UseProxyActor && ActorProxy)
	{
		ArrowProxy = ActorProxy;
	}
	else
	{
		ArrowProxy = Cast<AActor>( UGameplayStatics::GetPlayerPawn(GetWorld(), 0) );
	}

	if (ArrowProxy)
	{
		FVector Center = (BottomRight->GetActorLocation() + TopLeft->GetActorLocation()) / 2;
		FVector Translation = ArrowProxy->GetActorLocation() - Center;

		
		FVector RotTranslation = FRotator(0, AngleOffset, 0).RotateVector(Translation);
		
		FVector2D RotTranslation2D(RotTranslation.X, RotTranslation.Y);

		FWidgetTransform RT = IMG_Pawn->RenderTransform;
		RT.Translation = RotTranslation2D / ScaleFactor;

		RT.Angle = GetCurrentYaw() + AngleOffset;

		SizeBox_forPawn->SetRenderTransform(RT);
	}
}

void UMiniMapWidgetBase::InitMinimap()
{
	UWorld* World = GetWorld();

	if (!World)
		return;

	for (TActorIterator<AActor> It(World); It; ++It)
	{
		AMiniMapPosition* MiniMapPositionActor = *It ? Cast<AMiniMapPosition>(*It) : nullptr;
		if (MiniMapPositionActor)
		{
			FString ActorName = MiniMapPositionActor->PositionName;
			if (ActorName == ActorNameA)
			{
				TopLeft = MiniMapPositionActor;
			}
			else if (ActorName == ActorNameB)
			{
				BottomRight = MiniMapPositionActor;
			}
		}
		
		AActor* TempActor = *It;
		if (UseProxyActor && TempActor->ActorHasTag(FName(*ProxyActorTag)))
		{
			ActorProxy = TempActor;
		}
	}

	if (SizeBox_forPawn && SizeBox_forMap && TopLeft && BottomRight)
	{
		bReady = true;


		FRotationMatrix RT(FRotator(0, AngleOffset, 0));
		FVector WorldDistance = BottomRight->GetActorLocation() - TopLeft->GetActorLocation();//RT.TransformVector(BottomRight->GetActorLocation()) - RT.TransformVector(TopLeft->GetActorLocation());
		FVector2D WorldDistance2D = FVector2D(WorldDistance.X, WorldDistance.Y);
		FVector2D MapDistance = FVector2D(SizeBox_forMap->WidthOverride, SizeBox_forMap->HeightOverride);
		ScaleFactor = WorldDistance2D.Size() / MapDistance.Size();
	}
}

float UMiniMapWidgetBase::GetCurrentYaw()
{
	if (UseProxyActor && ActorProxy)
		return ActorProxy->GetActorRotation().Yaw;
	else
		return UGameplayStatics::GetPlayerController(GetWorld(), 0)->PlayerCameraManager->GetCameraRotation().Yaw;
}