<!DOCTYPE html>
<html>
    <head>
    <meta charset="utf-8"/>
        <title>instructions</title>
    </head>
    <body>
        <h3>Cefweb is an enterprise browser plug-in designed for unreal engine. It adopts many advanced technologies. It doesn't get stuck when browsing web pages or watching videos. The kernel adopts the latest CEF version and optimizes it. It has GPU binding function, supports the separation of game and browser rendering, and adds rendering cache inside the plug-in to make the rendering effect smooth. In terms of communication with web pages, V8 technology is adopted to make the Unreal Engine interact with web pages in real time. It is recommended to use Unreal Engine to render scenes and JavaScript for UI interaction, which can greatly improve the efficiency of system development.</h3>
        <h4>features:<h4>
        <p>1. H264 and h265 protocols</p>
        <p>2. Various video websites, such as youtube, bilibilibili</p>
        <p>3. Webrtc access cloud rendering</p>
        <p>4. Webrtc online voice and video</p>
        <p>5. H5, HTTPS, CSS and JavaScript</p>
        <p>6. UE4 and JavaScript call each other</p>
        <p>7. Mouse event transparent background page, and mouse penetration transparency can be adjusted</p>
        <p>8. Page navigation</p>
        <p>9. Debugging JavaScript with chrome tools</p>
        <p>10. Mouse drag</p>
        <p>11. Input method switching</p>
        <p>12. Ctrl + scroll wheel zoom page</p>
        <p>13. Load custom protocols on Web pages, such as SVN</p>
        <p>14. 8k video 60fps picture does not drop frames</p>
        <p>15. GPU binding separates unreal engine and browser rendering</p>
        <h3>For more information, please contact:</h3>
        <p>QQ: 249838638<br>WeChat: aSurgingRiver<br>Email: <EMAIL></p>
    </body>
</html>

